from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class DingTalkConfigBase(BaseModel):
    plan_id: int = Field(..., description="测试计划ID")
    plan_type: str = Field(..., description="计划类型：api-接口测试计划，functional-功能测试计划")
    webhook_url: str = Field(..., description="钉钉机器人webhook地址", max_length=500)
    secret: Optional[str] = Field(None, description="钉钉机器人密钥", max_length=200)
    send_condition: str = Field("all", description="发送条件：success-仅成功，failed-仅失败，all-全部")
    is_enabled: bool = Field(True, description="是否启用")


class DingTalkConfigCreate(DingTalkConfigBase):
    pass


class DingTalkConfigUpdate(BaseModel):
    id: int = Field(..., description="配置ID")
    webhook_url: Optional[str] = Field(None, description="钉钉机器人webhook地址", max_length=500)
    secret: Optional[str] = Field(None, description="钉钉机器人密钥", max_length=200)
    send_condition: Optional[str] = Field(None, description="发送条件：success-仅成功，failed-仅失败，all-全部")
    is_enabled: Optional[bool] = Field(None, description="是否启用")


class DingTalkConfigResponse(DingTalkConfigBase):
    """钉钉配置响应"""
    model_config = {"protected_namespaces": (), "from_attributes": True}

    id: int = Field(..., description="配置ID")
    user_id: int = Field(..., description="创建用户ID")
    created_at: str = Field(..., description="创建时间")
    updated_at: str = Field(..., description="更新时间")
