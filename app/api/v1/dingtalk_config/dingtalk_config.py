import logging
from fastapi import APIRouter, Body, Query
from app.controllers.dingtalk_config import dingtalk_config_controller
from app.schemas.base import Fail, Success
from app.schemas.dingtalk_config import (
    DingTalkConfigCreate,
    DingTalkConfigUpdate,
    DingTalkConfigResponse
)
from app.core.ctx import CTX_USER_ID
from app.core.dependency import DependAuth

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/get", summary="获取钉钉配置")
async def get_dingtalk_config(
    plan_id: int = Query(..., description="测试计划ID"),
    plan_type: str = Query(..., description="计划类型：api或functional"),
):
    """获取指定测试计划的钉钉配置"""
    try:
        config = await dingtalk_config_controller.get_by_plan(plan_id, plan_type)
        if config:
            config_dict = await config.to_dict()
            return Success(data=config_dict)
        else:
            return Success(data=None, msg="未找到钉钉配置")
    except Exception as e:
        logger.error(f"获取钉钉配置失败: {str(e)}")
        return Fail(msg=f"获取钉钉配置失败: {str(e)}")


@router.post("/save", summary="保存钉钉配置", dependencies=[DependAuth])
async def save_dingtalk_config(
    config_in: DingTalkConfigCreate = Body(...),
):
    """保存钉钉配置（更新或创建）"""
    try:
        user_id = CTX_USER_ID.get()
        config = await dingtalk_config_controller.update_or_create(
            plan_id=config_in.plan_id,
            plan_type=config_in.plan_type,
            obj_in=config_in,
            user_id=user_id
        )
        config_dict = await config.to_dict()
        return Success(data=config_dict, msg="钉钉配置保存成功")
    except Exception as e:
        logger.error(f"保存钉钉配置失败: {str(e)}")
        return Fail(msg=f"保存钉钉配置失败: {str(e)}")


@router.delete("/delete", summary="删除钉钉配置", dependencies=[DependAuth])
async def delete_dingtalk_config(
    plan_id: int = Query(..., description="测试计划ID"),
    plan_type: str = Query(..., description="计划类型：api或functional"),
):
    """删除钉钉配置"""
    try:
        success = await dingtalk_config_controller.delete_by_plan(plan_id, plan_type)
        if success:
            return Success(msg="钉钉配置删除成功")
        else:
            return Fail(msg="未找到要删除的钉钉配置")
    except Exception as e:
        logger.error(f"删除钉钉配置失败: {str(e)}")
        return Fail(msg=f"删除钉钉配置失败: {str(e)}")


@router.post("/test", summary="测试钉钉配置", dependencies=[DependAuth])
async def test_dingtalk_config(
    config_in: DingTalkConfigCreate = Body(...),
):
    """测试钉钉配置是否有效"""
    try:
        from app.utils.dingtalk_bot import DingTalkBot
        
        # 创建临时钉钉机器人实例
        bot = DingTalkBot(webhook_url=config_in.webhook_url, secret=config_in.secret)
        
        # 发送测试消息
        test_message = "这是一条测试消息，用于验证钉钉机器人配置是否正确。"
        result = await bot.send_text(test_message)
        
        if result.get('success'):
            return Success(msg="钉钉配置测试成功")
        else:
            return Fail(msg=f"钉钉配置测试失败: {result.get('message')}")
    except Exception as e:
        logger.error(f"测试钉钉配置失败: {str(e)}")
        return Fail(msg=f"测试钉钉配置失败: {str(e)}")
