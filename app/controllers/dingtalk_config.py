import logging
from typing import Optional, Dict, Any
from app.core.crud import CRUDBase
from app.models.admin import DingTalkConfig
from app.schemas.dingtalk_config import DingTalkConfigCreate, DingTalkConfigUpdate

logger = logging.getLogger(__name__)


class DingTalkConfigController(CRUDBase[DingTalkConfig, DingTalkConfigCreate, DingTalkConfigUpdate]):
    def __init__(self):
        super().__init__(model=DingTalkConfig)

    async def create_with_user(self, obj_in: DingTalkConfigCreate, user_id: int) -> DingTalkConfig:
        """创建钉钉配置并关联用户"""
        obj_data = obj_in.dict()
        obj_data['user_id'] = user_id
        return await self.model.create(**obj_data)

    async def get_by_plan(self, plan_id: int, plan_type: str) -> Optional[DingTalkConfig]:
        """根据计划ID和类型获取钉钉配置"""
        try:
            return await self.model.filter(plan_id=plan_id, plan_type=plan_type).first()
        except Exception as e:
            logger.error(f"获取钉钉配置失败: {str(e)}")
            return None

    async def update_or_create(self, plan_id: int, plan_type: str, obj_in: DingTalkConfigCreate, user_id: int) -> DingTalkConfig:
        """更新或创建钉钉配置"""
        existing_config = await self.get_by_plan(plan_id, plan_type)
        
        if existing_config:
            # 更新现有配置
            obj_data = obj_in.dict()
            obj_data.pop('plan_id', None)  # 移除不可更新的字段
            obj_data.pop('plan_type', None)
            
            for key, value in obj_data.items():
                setattr(existing_config, key, value)
            
            await existing_config.save()
            return existing_config
        else:
            # 创建新配置
            return await self.create_with_user(obj_in, user_id)

    async def delete_by_plan(self, plan_id: int, plan_type: str) -> bool:
        """根据计划ID和类型删除钉钉配置"""
        try:
            config = await self.get_by_plan(plan_id, plan_type)
            if config:
                await config.delete()
                return True
            return False
        except Exception as e:
            logger.error(f"删除钉钉配置失败: {str(e)}")
            return False


dingtalk_config_controller = DingTalkConfigController()
