import logging
from datetime import datetime
from typing import List, <PERSON><PERSON>, Optional, Dict, Any
from tortoise.expressions import Q
from tortoise.transactions import in_transaction

from app.core.crud import CRUDBase
from app.models.admin import ApiTestPlan, ApiTestPlanCase, ApiTestCase, User, Project, Environment
from app.schemas.api_test_plan import ApiTestPlanCreate, ApiTestPlanUpdate

logger = logging.getLogger(__name__)


class ApiTestPlanController(CRUDBase[ApiTestPlan, ApiTestPlanCreate, ApiTestPlanUpdate]):
    def __init__(self):
        super().__init__(model=ApiTestPlan)

    async def list_by_project_with_user_info(
        self, 
        project_id: int, 
        page: int = 1, 
        page_size: int = 10, 
        search: Q = None
    ) -> Tuple[int, List[dict]]:
        """获取项目下的测试计划列表，包含用户信息"""
        query = self.model.filter(project_id=project_id)
        if search:
            query = query.filter(search)
        
        total = await query.count()
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 获取数据
        plans = await query.offset(offset).limit(page_size).order_by('-created_at')
        
        # 获取用户信息和环境信息
        result = []
        for plan in plans:
            plan_dict = await plan.to_dict()
            
            # 获取创建人信息
            try:
                user = await User.get(id=plan.user_id)
                plan_dict['creator_name'] = user.alias or user.username
            except:
                plan_dict['creator_name'] = '未知用户'
            
            # 获取环境信息
            if plan.environment_id:
                try:
                    environment = await Environment.get(id=plan.environment_id)
                    plan_dict['environment_name'] = environment.name  # 使用环境名称
                except:
                    plan_dict['environment_name'] = '环境已删除'
            else:
                plan_dict['environment_name'] = None
            
            result.append(plan_dict)
        
        return total, result

    async def list_with_user_info(
        self, 
        page: int = 1, 
        page_size: int = 10, 
        search: Q = None
    ) -> Tuple[int, List[dict]]:
        """获取所有测试计划列表，包含用户信息"""
        query = self.model.all()
        if search:
            query = query.filter(search)
        
        total = await query.count()
        
        # 计算偏移量
        offset = (page - 1) * page_size
        
        # 获取数据
        plans = await query.offset(offset).limit(page_size).order_by('-created_at')
        
        # 获取用户信息和环境信息
        result = []
        for plan in plans:
            plan_dict = await plan.to_dict()
            
            # 获取创建人信息
            try:
                user = await User.get(id=plan.user_id)
                plan_dict['creator_name'] = user.alias or user.username
            except:
                plan_dict['creator_name'] = '未知用户'
            
            # 获取环境信息
            if plan.environment_id:
                try:
                    environment = await Environment.get(id=plan.environment_id)
                    plan_dict['environment_name'] = environment.name  # 使用环境名称
                except:
                    plan_dict['environment_name'] = '环境已删除'
            else:
                plan_dict['environment_name'] = None
            
            result.append(plan_dict)
        
        return total, result

    async def create_with_user(self, obj_in: ApiTestPlanCreate, user_id: int) -> ApiTestPlan:
        """创建测试计划"""
        obj_data = obj_in.model_dump()
        obj_data['user_id'] = user_id
        return await self.create(obj_in=obj_data)

    async def copy_plan(self, plan_id: int, user_id: int) -> ApiTestPlan:
        """复制测试计划"""
        async with in_transaction():
            # 获取原计划
            original_plan = await self.get(id=plan_id)
            
            # 创建新计划
            new_plan_data = {
                'plan_name': f"{original_plan.plan_name}_副本",
                'level': original_plan.level,
                'status': 'not_started',
                'description': original_plan.description,
                'project_id': original_plan.project_id,
                'environment_id': original_plan.environment_id,
                'user_id': user_id
            }
            new_plan = await self.create(obj_in=new_plan_data)
            
            # 复制关联的测试用例
            original_cases = await ApiTestPlanCase.filter(plan_id=plan_id)
            for case in original_cases:
                await ApiTestPlanCase.create(
                    plan_id=new_plan.id,
                    case_id=case.case_id,
                    execution_order=case.execution_order
                )
            
            return new_plan

    async def get_plan_cases(self, plan_id: int) -> List[dict]:
        """获取测试计划关联的测试用例"""
        try:
            plan_cases = await ApiTestPlanCase.filter(plan_id=plan_id).order_by('execution_order')

            result = []
            for plan_case in plan_cases:
                try:
                    test_case = await ApiTestCase.get(id=plan_case.case_id)
                    case_dict = await test_case.to_dict()
                    case_dict['execution_order'] = plan_case.execution_order
                    case_dict['execution_status'] = plan_case.execution_status
                    case_dict['execution_result'] = plan_case.execution_result
                    # 处理datetime对象的序列化
                    if plan_case.execution_time:
                        case_dict['execution_time'] = plan_case.execution_time.strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        case_dict['execution_time'] = None
                    case_dict['response_time'] = plan_case.response_time
                    case_dict['error_message'] = plan_case.error_message
                    # 新增响应相关字段
                    case_dict['status_code'] = plan_case.status_code
                    case_dict['response_headers'] = plan_case.response_headers

                    # 处理response_body，确保返回正确的JSON格式
                    if plan_case.response_body:
                        try:
                            # 如果是字符串，尝试使用ast.literal_eval解析Python字典格式
                            import ast
                            if isinstance(plan_case.response_body, str):
                                # 尝试解析Python字典格式的字符串
                                parsed_body = ast.literal_eval(plan_case.response_body)
                                case_dict['response_body'] = parsed_body
                            else:
                                case_dict['response_body'] = plan_case.response_body
                        except (ValueError, SyntaxError):
                            # 如果解析失败，直接返回原始字符串
                            case_dict['response_body'] = plan_case.response_body
                    else:
                        case_dict['response_body'] = plan_case.response_body

                    case_dict['assertion_results'] = plan_case.assertion_results
                    result.append(case_dict)
                except Exception as e:
                    # 测试用例已被删除或其他错误，记录日志但跳过
                    logger.warning(f"获取测试用例 {plan_case.case_id} 失败: {str(e)}")
                    continue

            return result
        except Exception as e:
            logger.error(f"获取测试计划 {plan_id} 关联用例失败: {str(e)}")
            raise e

    async def add_cases_to_plan(self, plan_id: int, case_ids: List[int]) -> bool:
        """添加测试用例到测试计划"""
        async with in_transaction():
            # 获取当前最大执行顺序
            existing_cases = await ApiTestPlanCase.filter(plan_id=plan_id).order_by('-execution_order')
            max_order = existing_cases[0].execution_order if existing_cases else 0

            for i, case_id in enumerate(case_ids):
                # 检查是否已存在
                existing = await ApiTestPlanCase.filter(plan_id=plan_id, case_id=case_id).first()
                if not existing:
                    await ApiTestPlanCase.create(
                        plan_id=plan_id,
                        case_id=case_id,
                        execution_order=max_order + i + 1
                    )
        return True

    async def remove_cases_from_plan(self, plan_id: int, case_ids: List[int]) -> bool:
        """从测试计划中移除测试用例"""
        async with in_transaction():
            # 删除指定用例
            await ApiTestPlanCase.filter(plan_id=plan_id, case_id__in=case_ids).delete()

            # 重新排序剩余用例，确保顺序连续
            remaining_cases = await ApiTestPlanCase.filter(plan_id=plan_id).order_by('execution_order')
            for index, case in enumerate(remaining_cases):
                await ApiTestPlanCase.filter(id=case.id).update(execution_order=index + 1)

        return True

    async def get_approved_cases_by_project(self, project_id: int) -> List[dict]:
        """获取项目下已审核的测试用例"""
        cases = await ApiTestCase.filter(
            project_id=project_id,
            status='approved'
        ).order_by('-created_at')

        result = []
        for case in cases:
            case_dict = await case.to_dict()
            result.append(case_dict)

        return result

    async def update_case_order(self, plan_id: int, case_orders: List[dict]) -> bool:
        """更新测试用例执行顺序"""
        try:
            async with in_transaction():
                for order_info in case_orders:
                    case_id = order_info.get('case_id') if isinstance(order_info, dict) else order_info.case_id
                    execution_order = order_info.get('execution_order') if isinstance(order_info, dict) else order_info.execution_order

                    await ApiTestPlanCase.filter(
                        plan_id=plan_id,
                        case_id=case_id
                    ).update(execution_order=execution_order)
            return True
        except Exception as e:
            logger.error(f"更新用例顺序失败: {str(e)}")
            raise e

    async def execute_test_plan(self, plan_id: int, environment_id: int) -> dict:
        """执行测试计划"""
        # 这里是一个简化的执行逻辑，实际项目中可能需要更复杂的执行引擎
        async with in_transaction():
            # 更新计划状态为执行中
            await self.update(id=plan_id, obj_in={
                'status': 'in_progress',
                'execution_result': 'running',
                'last_execution_time': datetime.now()
            })
            
            # 获取计划关联的测试用例
            plan_cases = await ApiTestPlanCase.filter(plan_id=plan_id).order_by('execution_order')
            
            total_cases = len(plan_cases)
            passed_cases = 0
            
            # 模拟执行每个测试用例
            for plan_case in plan_cases:
                try:
                    # 这里应该调用实际的测试用例执行逻辑
                    # 现在只是模拟执行结果
                    import random
                    success = random.choice([True, True, True, False])  # 75%成功率
                    
                    if success:
                        passed_cases += 1
                        await ApiTestPlanCase.filter(id=plan_case.id).update(
                            execution_result='success',
                            execution_time=datetime.now(),
                            response_time=random.randint(100, 2000)
                        )
                    else:
                        await ApiTestPlanCase.filter(id=plan_case.id).update(
                            execution_result='failed',
                            execution_time=datetime.now(),
                            response_time=random.randint(100, 2000),
                            error_message='模拟执行失败'
                        )
                except Exception as e:
                    await ApiTestPlanCase.filter(id=plan_case.id).update(
                        execution_result='failed',
                        execution_time=datetime.now(),
                        error_message=str(e)
                    )
            
            # 计算通过率
            pass_rate = (passed_cases / total_cases * 100) if total_cases > 0 else 0
            
            # 更新计划执行结果
            final_result = 'success' if passed_cases == total_cases else 'failed'
            await self.update(id=plan_id, obj_in={
                'status': 'completed',
                'execution_result': final_result,
                'pass_rate': round(pass_rate, 2),
                'last_execution_time': datetime.now()
            })
            
            return {
                'total_cases': total_cases,
                'passed_cases': passed_cases,
                'failed_cases': total_cases - passed_cases,
                'pass_rate': round(pass_rate, 2),
                'execution_result': final_result
            }

    async def batch_execute_test_plan(self, plan_id: int, environment_id: int) -> Dict[str, Any]:
        """批量执行测试计划（支持用例间参数传递）"""
        try:
            # 导入API测试用例控制器和变量管理器
            from app.controllers.api_test_case import api_test_case_controller
            from app.utils.variable_manager import VariableManager

            # 创建变量管理器实例，用于整个测试计划的执行过程
            variable_manager = VariableManager()

            # 更新计划状态为执行中
            await self.update(id=plan_id, obj_in={
                'status': 'in_progress',
                'execution_result': 'running',
                'last_execution_time': datetime.now()
            })

            # 获取测试计划关联的用例
            plan_cases = await self.get_plan_cases(plan_id)

            if not plan_cases:
                await self.update(id=plan_id, obj_in={
                    'status': 'completed',
                    'execution_result': 'failed',
                    'last_execution_time': datetime.now()
                })
                return {
                    'success': False,
                    'error_message': '测试计划中没有关联的测试用例',
                    'total_cases': 0,
                    'passed_cases': 0,
                    'failed_cases': 0,
                    'pass_rate': 0.0,
                    'execution_result': 'failed',
                    'case_results': []
                }

            # 过滤出执行状态为"正常"的用例
            normal_cases = [case for case in plan_cases if case.get('execution_status', 'normal') == 'normal']

            logger.info(f"开始批量执行测试计划 {plan_id}，共 {len(plan_cases)} 个用例，其中 {len(normal_cases)} 个正常状态用例将被执行")

            # 按执行顺序排序
            sorted_cases = sorted(normal_cases, key=lambda x: x.get('execution_order', 0))

            total_cases = len(sorted_cases)
            passed_cases = 0
            failed_cases = 0
            case_results = []
            total_execution_time = 0

            # 依次执行每个测试用例，传递变量管理器以支持参数传递
            for case in sorted_cases:
                try:
                    logger.info(f"执行测试用例: {case.get('case_name')} (ID: {case.get('id')})")
                    logger.info(f"当前变量状态: {variable_manager.get_variables()}")

                    # 调用单个用例执行方法，传入变量管理器
                    execution_result = await api_test_case_controller.execute_test_case(
                        case.get('id'), environment_id, variable_manager
                    )

                    # 判断执行是否成功
                    success = execution_result.get('success', False)

                    if success:
                        passed_cases += 1
                    else:
                        failed_cases += 1

                    # 累计执行时间
                    total_execution_time += execution_result.get('execution_time', 0)

                    # 保存用例执行结果（无论成功失败都保存完整信息）
                    case_result = {
                        'case_id': case.get('id'),
                        'case_name': case.get('case_name'),
                        'success': success,
                        'status_code': execution_result.get('status_code'),
                        'response_time': execution_result.get('execution_time', 0),
                        'error_message': execution_result.get('error_message'),
                        'response_headers': execution_result.get('response_headers', {}),
                        'response_body': execution_result.get('response_body'),
                        'assertion_results': execution_result.get('assertion_results', {}).get('results', []) if execution_result.get('assertion_results') else []
                    }
                    case_results.append(case_result)

                    logger.info(f"用例执行完成: {case.get('case_name')}, 结果: {'成功' if success else '失败'}")

                except Exception as e:
                    logger.error(f"执行用例 {case.get('case_name')} 失败: {str(e)}")
                    failed_cases += 1
                    case_results.append({
                        'case_id': case.get('id'),
                        'case_name': case.get('case_name'),
                        'success': False,
                        'status_code': None,
                        'response_time': 0,
                        'error_message': f"执行异常: {str(e)}",
                        'response_headers': {},
                        'response_body': None,
                        'assertion_results': []
                    })

            # 更新每个用例的执行结果到数据库
            await self._update_case_execution_results(plan_id, case_results)

            # 计算通过率
            pass_rate = (passed_cases / total_cases * 100) if total_cases > 0 else 0

            # 更新计划执行结果
            final_result = 'success' if passed_cases == total_cases else 'failed'
            await self.update(id=plan_id, obj_in={
                'status': 'completed',
                'execution_result': final_result,
                'pass_rate': round(pass_rate, 2),
                'last_execution_time': datetime.now()
            })

            logger.info(f"批量执行完成: 总计 {total_cases} 个用例，通过 {passed_cases} 个，失败 {failed_cases} 个，通过率 {pass_rate:.2f}%")

            # 构建返回结果
            result = {
                'success': True,
                'total_cases': total_cases,
                'passed_cases': passed_cases,
                'failed_cases': failed_cases,
                'pass_rate': round(pass_rate, 2),
                'execution_result': final_result,
                'case_results': case_results,
                'execution_time': total_execution_time
            }

            # 检查是否启用钉钉通知
            plan = await self.get(id=plan_id)
            if plan and plan.enable_dingtalk:
                try:
                    await self._send_dingtalk_notification(plan_id, result)
                except Exception as e:
                    logger.error(f"发送钉钉通知失败: {str(e)}")
                    # 不影响主流程，继续返回结果
            else:
                logger.info(f"测试计划 {plan_id} 未启用钉钉通知，跳过发送")

            return result

        except Exception as e:
            logger.error(f"批量执行测试计划失败: {str(e)}")

            # 更新计划状态为失败
            await self.update(id=plan_id, obj_in={
                'status': 'completed',
                'execution_result': 'failed',
                'last_execution_time': datetime.now()
            })

            return {
                'success': False,
                'error_message': str(e),
                'total_cases': len(plan_cases) if 'plan_cases' in locals() else 0,
                'passed_cases': 0,
                'failed_cases': len(plan_cases) if 'plan_cases' in locals() else 0,
                'pass_rate': 0.0,
                'execution_result': 'failed',
                'case_results': []
            }

    async def _update_case_execution_results(self, plan_id: int, case_results: List[Dict]) -> None:
        """更新用例执行结果"""
        try:
            async with in_transaction():
                for result in case_results:
                    case_id = result.get('case_id')
                    success = result.get('success', False)
                    response_time = result.get('response_time', 0)
                    error_message = result.get('error_message')
                    status_code = result.get('status_code')
                    response_headers = result.get('response_headers')
                    response_body = result.get('response_body')
                    assertion_results = result.get('assertion_results')

                    # 构建更新数据
                    update_data = {
                        'execution_result': 'success' if success else 'failed',
                        'execution_time': datetime.now(),
                        'response_time': int(response_time) if response_time else 0,
                        'error_message': error_message,
                        'status_code': status_code,
                        'response_headers': response_headers,
                        'response_body': response_body,
                        'assertion_results': assertion_results
                    }

                    await ApiTestPlanCase.filter(
                        plan_id=plan_id,
                        case_id=case_id
                    ).update(**update_data)

                    logger.info(f"更新用例 {case_id} 执行结果: {success}, 状态码: {status_code}")
        except Exception as e:
            logger.error(f"更新用例执行结果失败: {str(e)}")

    async def _send_dingtalk_notification(self, plan_id: int, execution_result: Dict[str, Any]) -> None:
        """发送钉钉通知"""
        try:
            from app.utils.dingtalk_bot import DingTalkBot
            from app.controllers.dingtalk_config import dingtalk_config_controller

            # 获取测试计划信息
            try:
                plan = await self.get(id=plan_id)
                plan_name = plan.plan_name if plan else f"测试计划{plan_id}"
            except Exception:
                plan_name = f"测试计划{plan_id}"

            # 获取钉钉配置
            dingtalk_config = await dingtalk_config_controller.get_by_plan(plan_id, "api")
            if not dingtalk_config or not dingtalk_config.is_enabled:
                logger.info(f"测试计划 {plan_name} 未配置钉钉通知或已禁用，跳过发送")
                return

            # 检查发送条件
            execution_status = execution_result.get('execution_result', 'failed')
            send_condition = dingtalk_config.send_condition

            should_send = False
            if send_condition == "all":
                should_send = True
            elif send_condition == "success" and execution_status == "success":
                should_send = True
            elif send_condition == "failed" and execution_status == "failed":
                should_send = True

            if not should_send:
                logger.info(f"测试计划 {plan_name} 根据发送条件 {send_condition} 跳过发送通知")
                return

            # 创建钉钉机器人实例
            bot = DingTalkBot(webhook_url=dingtalk_config.webhook_url, secret=dingtalk_config.secret)

            # 发送测试报告到钉钉
            result = await bot.send_test_report(execution_result, plan_name)

            if result.get('success'):
                logger.info(f"钉钉通知发送成功: 测试计划 {plan_name}")
            else:
                logger.error(f"钉钉通知发送失败: {result.get('message')}")

        except Exception as e:
            logger.error(f"发送钉钉通知异常: {str(e)}")

    async def get_statistics(self) -> List[Dict[str, Any]]:
        """获取接口测试计划统计数据，按项目分组"""
        try:
            from app.models.admin import Project

            # 获取所有项目
            projects = await Project.all().order_by('id')
            statistics = []

            for project in projects:
                project_dict = await project.to_dict()

                # 获取该项目下的测试计划统计
                plans = await self.model.filter(project_id=project.id).all()

                total_plans = len(plans)
                executed_plans = len([p for p in plans if p.last_execution_time is not None])
                success_plans = len([p for p in plans if p.execution_result == 'success'])
                failed_plans = len([p for p in plans if p.execution_result == 'failed'])

                # 计算平均通过率
                pass_rates = [p.pass_rate for p in plans if p.pass_rate is not None]
                avg_pass_rate = sum(pass_rates) / len(pass_rates) if pass_rates else 0

                # 获取最近执行时间
                recent_executions = [p.last_execution_time for p in plans if p.last_execution_time is not None]
                last_execution = max(recent_executions) if recent_executions else None

                project_stats = {
                    'project_id': project.id,
                    'project_name': project.name,
                    'total_plans': total_plans,
                    'executed_plans': executed_plans,
                    'success_plans': success_plans,
                    'failed_plans': failed_plans,
                    'avg_pass_rate': round(avg_pass_rate, 2),
                    'last_execution': last_execution.strftime('%Y-%m-%d %H:%M:%S') if last_execution else None
                }

                statistics.append(project_stats)

            return statistics

        except Exception as e:
            logger.error(f"获取统计数据失败: {str(e)}")
            return []

    async def update_case_execution_status(self, plan_id: int, case_id: int, execution_status: str) -> bool:
        """更新测试用例执行状态"""
        try:
            await ApiTestPlanCase.filter(
                plan_id=plan_id,
                case_id=case_id
            ).update(execution_status=execution_status)
            return True
        except Exception as e:
            logger.error(f"更新用例执行状态失败: {str(e)}")
            return False


api_test_plan_controller = ApiTestPlanController()
