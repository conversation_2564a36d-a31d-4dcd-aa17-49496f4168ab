from tortoise import fields

from app.schemas.menus import MenuType

from .base import BaseModel, TimestampMixin
from .enums import MethodType, EnvironmentType, AIModelType, AIModelStatus


class User(BaseModel, TimestampMixin):
    username = fields.CharField(max_length=20, unique=True, description="用户名称", index=True)
    alias = fields.CharField(max_length=30, null=True, description="姓名", index=True)
    email = fields.CharField(max_length=255, unique=True, description="邮箱", index=True)
    phone = fields.CharField(max_length=20, null=True, description="电话", index=True)
    password = fields.Char<PERSON>ield(max_length=128, null=True, description="密码")
    is_active = fields.BooleanField(default=True, description="是否激活", index=True)
    is_superuser = fields.BooleanField(default=False, description="是否为超级管理员", index=True)
    last_login = fields.DatetimeField(null=True, description="最后登录时间", index=True)
    roles = fields.ManyToManyField("models.Role", related_name="user_roles")
    dept_id = fields.IntField(null=True, description="部门ID", index=True)

    class Meta:
        table = "user"


class Role(BaseModel, TimestampMixin):
    name = fields.CharField(max_length=20, unique=True, description="角色名称", index=True)
    desc = fields.CharField(max_length=500, null=True, description="角色描述")
    menus = fields.ManyToManyField("models.Menu", related_name="role_menus")
    apis = fields.ManyToManyField("models.Api", related_name="role_apis")

    class Meta:
        table = "role"


class Api(BaseModel, TimestampMixin):
    path = fields.CharField(max_length=100, description="API路径", index=True)
    method = fields.CharEnumField(MethodType, description="请求方法", index=True)
    summary = fields.CharField(max_length=500, description="请求简介", index=True)
    tags = fields.CharField(max_length=100, description="API标签", index=True)

    class Meta:
        table = "api"


class Menu(BaseModel, TimestampMixin):
    name = fields.CharField(max_length=20, description="菜单名称", index=True)
    remark = fields.JSONField(null=True, description="保留字段")
    menu_type = fields.CharEnumField(MenuType, null=True, description="菜单类型")
    icon = fields.CharField(max_length=100, null=True, description="菜单图标")
    path = fields.CharField(max_length=100, description="菜单路径", index=True)
    order = fields.IntField(default=0, null=True, description="排序", index=True)
    parent_id = fields.IntField(default=0, max_length=10, description="父菜单ID", index=True)
    is_hidden = fields.BooleanField(default=False, description="是否隐藏")
    component = fields.CharField(max_length=100, description="组件")
    keepalive = fields.BooleanField(default=True, description="存活")
    redirect = fields.CharField(max_length=100, null=True, description="重定向")

    class Meta:
        table = "menu"


class Dept(BaseModel, TimestampMixin):
    name = fields.CharField(max_length=20, unique=True, description="部门名称", index=True)
    desc = fields.CharField(max_length=500, null=True, description="备注")
    is_deleted = fields.BooleanField(default=False, description="软删除标记", index=True)
    order = fields.IntField(default=0, description="排序", index=True)
    parent_id = fields.IntField(default=0, max_length=10, description="父部门ID", index=True)

    class Meta:
        table = "dept"


class DeptClosure(BaseModel, TimestampMixin):
    ancestor = fields.IntField(description="父代", index=True)
    descendant = fields.IntField(description="子代", index=True)
    level = fields.IntField(default=0, description="深度", index=True)


class AuditLog(BaseModel, TimestampMixin):
    user_id = fields.IntField(description="用户ID", index=True)
    username = fields.CharField(max_length=64, default="", description="用户名称", index=True)
    module = fields.CharField(max_length=64, default="", description="功能模块", index=True)
    summary = fields.CharField(max_length=128, default="", description="请求描述", index=True)
    method = fields.CharField(max_length=10, default="", description="请求方法", index=True)
    path = fields.CharField(max_length=255, default="", description="请求路径", index=True)
    status = fields.IntField(default=-1, description="状态码", index=True)
    response_time = fields.IntField(default=0, description="响应时间(单位ms)", index=True)


class ApiRequest(BaseModel, TimestampMixin):
    api_name = fields.CharField(max_length=100, description="接口名称", index=True)
    url = fields.CharField(max_length=500, description="请求URL", index=True)
    method = fields.CharEnumField(MethodType, description="请求方法", index=True)
    params = fields.JSONField(null=True, description="请求参数")
    headers = fields.JSONField(null=True, description="请求头")
    body = fields.TextField(null=True, description="请求体")
    description = fields.TextField(null=True, description="接口描述")
    user_id = fields.IntField(null=True, description="创建用户ID", index=True)
    project_id = fields.IntField(null=True, description="所属项目ID", index=True)
    module_id = fields.IntField(null=True, description="所属模块ID", index=True)
    category = fields.CharField(max_length=50, null=True, description="接口分类", index=True)
    is_favorite = fields.BooleanField(default=False, description="是否收藏", index=True)
    last_executed = fields.DatetimeField(null=True, description="最后执行时间", index=True)
    execution_count = fields.IntField(default=0, description="执行次数")

    class Meta:
        table = "api_request"
        table_description = "API请求记录表"


class Project(BaseModel, TimestampMixin):
    name = fields.CharField(max_length=100, description="项目名称")
    description = fields.TextField(null=True, description="项目描述")
    status = fields.CharField(max_length=20, description="项目状态")
    start_date = fields.DatetimeField(null=True, description="开始日期")
    end_date = fields.DatetimeField(null=True, description="结束日期")
    manager = fields.CharField(max_length=50, null=True, description="项目经理")
    budget = fields.FloatField(default=0, description="项目预算")

    class Meta:
        table = "projects"
        table_description = "项目表"


class ProjectModule(BaseModel, TimestampMixin):
    name = fields.CharField(max_length=100, description="模块名称", index=True)
    description = fields.TextField(null=True, description="模块描述")
    project_id = fields.IntField(description="所属项目ID", index=True)
    parent_id = fields.IntField(description="父模块ID", default=0, index=True)
    order = fields.IntField(description="排序", default=0, index=True)
    status = fields.CharField(max_length=20, description="模块状态", default="active")

    class Meta:
        table = "project_modules"
        table_description = "项目模块表"


class ApiTestCase(BaseModel, TimestampMixin):
    case_number = fields.CharField(max_length=50, unique=True, description="用例编号", index=True)
    case_name = fields.CharField(max_length=200, description="用例名称", index=True)
    method = fields.CharEnumField(MethodType, description="请求方式", index=True)
    url = fields.CharField(max_length=500, description="请求URL")
    params = fields.JSONField(null=True, description="请求参数")
    body = fields.TextField(null=True, description="请求体")
    expected_result = fields.TextField(null=True, description="断言配置（JSON格式）")
    variable_extracts = fields.TextField(null=True, description="变量提取配置（JSON格式）")
    is_smoke = fields.BooleanField(default=False, description="是否冒烟用例", index=True)
    status = fields.CharField(max_length=20, default="pending", description="状态：pending-待审核，approved-已审核", index=True)
    source = fields.CharField(max_length=20, default="manual", description="来源：manual-人工，ai-AI", index=True)
    project_id = fields.IntField(description="所属项目ID", index=True)
    module_id = fields.IntField(null=True, description="所属模块ID", index=True)
    user_id = fields.IntField(null=True, description="创建用户ID", index=True)

    class Meta:
        table = "api_test_cases"
        table_description = "接口测试用例表"


class ApiImport(BaseModel, TimestampMixin):
    api_name = fields.CharField(max_length=100, description="接口名称说明", index=True)
    url_path = fields.CharField(max_length=500, description="接口路径", index=True)
    method = fields.CharEnumField(MethodType, description="请求方式", index=True)
    params_list = fields.TextField(null=True, description="请求参数")
    status = fields.CharField(max_length=20, description="状态")
    project_id = fields.IntField(null=True, description="所属项目ID", index=True)

    class Meta:
        table = "api_import"
        table_description = "API导入"


class Environment(BaseModel, TimestampMixin):
    name = fields.CharField(max_length=100, description="环境名称", index=True)
    env_type = fields.CharEnumField(EnvironmentType, description="环境类型", index=True)
    host = fields.CharField(max_length=255, description="主机地址", index=True)
    port = fields.IntField(null=True, description="端口号")
    token = fields.TextField(null=True, description="访问令牌")
    prefix = fields.TextField(null=True, description="令牌前缀")
    description = fields.TextField(null=True, description="环境描述")
    project_id = fields.IntField(description="所属项目ID", index=True)
    is_active = fields.BooleanField(default=True, description="是否启用", index=True)
    # Token自动获取配置
    token_url = fields.CharField(max_length=500, null=True, description="Token获取URL")
    # 验证码配置
    enable_captcha = fields.BooleanField(default=False, description="是否启用验证码")
    captcha_url = fields.CharField(max_length=500, null=True, description="验证码获取URL")
    captcha_method = fields.CharField(max_length=10, default="GET", description="验证码获取请求方式")
    captcha_headers = fields.JSONField(null=True, description="验证码获取请求头")
    captcha_body = fields.JSONField(null=True, description="验证码获取请求体")
    captcha_image_path = fields.CharField(max_length=200, default="content.imageBase64", description="验证码图片字段路径(JSONPath)")
    captcha_key_path = fields.CharField(max_length=200, default="content.codeKey", description="验证码Key字段路径(JSONPath)")
    token_method = fields.CharField(max_length=10, default="POST", description="Token获取请求方式")
    token_headers = fields.JSONField(null=True, description="Token获取请求头")
    token_body = fields.JSONField(null=True, description="Token获取请求体")
    token_field_name = fields.CharField(max_length=100, default="token", description="Token字段名称")
    token_field_path = fields.CharField(max_length=200, null=True, description="Token字段路径(JSONPath)")
    auto_refresh_token = fields.BooleanField(default=False, description="是否自动刷新Token")
    token_refresh_interval = fields.IntField(default=3600, description="Token刷新间隔(秒)")
    last_token_refresh = fields.DatetimeField(null=True, description="最后Token刷新时间")

    class Meta:
        table = "environments"
        table_description = "环境配置表"


class ApiExecutionHistory(BaseModel, TimestampMixin):
    user_id = fields.IntField(description="用户ID", index=True)
    method = fields.CharEnumField(MethodType, description="请求方法", index=True)
    url = fields.CharField(max_length=500, description="请求URL")  # 移除index，避免索引过长
    params = fields.JSONField(null=True, description="请求参数")
    headers = fields.JSONField(null=True, description="请求头")
    body = fields.TextField(null=True, description="请求体")
    status_code = fields.IntField(null=True, description="响应状态码")
    response_time = fields.IntField(null=True, description="响应时间(毫秒)")
    success = fields.BooleanField(default=True, description="是否成功")
    # 新增响应相关字段
    response_headers = fields.JSONField(null=True, description="响应头")
    response_body = fields.TextField(null=True, description="响应体")
    response_size = fields.CharField(max_length=50, null=True, description="响应大小")
    response_status_text = fields.CharField(max_length=100, null=True, description="响应状态文本")

    class Meta:
        table = "api_execution_history"
        table_description = "API执行历史记录表"


class TestCase(BaseModel, TimestampMixin):
    case_number = fields.CharField(max_length=50, unique=True, description="用例编号", index=True)
    case_name = fields.CharField(max_length=200, description="用例名称", index=True)
    case_level = fields.CharField(max_length=10, default="medium", description="用例等级：low-低，medium-中，high-高", index=True)
    precondition = fields.TextField(null=True, description="前置条件")
    test_steps = fields.TextField(description="用例步骤")
    expected_result = fields.TextField(description="预期结果")
    is_smoke = fields.BooleanField(default=False, description="是否冒烟用例", index=True)
    status = fields.CharField(max_length=20, default="pending", description="状态：pending-待审核，approved-已审核", index=True)
    source = fields.CharField(max_length=20, default="manual", description="来源：manual-人工，ai-AI", index=True)
    project_id = fields.IntField(description="所属项目ID", index=True)
    module_id = fields.IntField(null=True, description="所属模块ID", index=True)
    user_id = fields.IntField(null=True, description="创建用户ID", index=True)

    class Meta:
        table = "test_cases"
        table_description = "功能测试用例表"


class ApiTestPlan(BaseModel, TimestampMixin):
    plan_name = fields.CharField(max_length=200, description="计划名称", index=True)
    level = fields.CharField(max_length=10, default="medium", description="等级：low-低，medium-中，high-高", index=True)
    status = fields.CharField(max_length=20, default="not_started", description="状态：not_started-未开始，in_progress-进行中，completed-已完成", index=True)
    description = fields.TextField(null=True, description="计划描述")
    project_id = fields.IntField(description="所属项目ID", index=True)
    environment_id = fields.IntField(null=True, description="运行环境ID", index=True)
    execution_result = fields.CharField(max_length=20, null=True, description="执行结果：success-成功，failed-失败，running-执行中", index=True)
    pass_rate = fields.FloatField(null=True, description="通过率")
    last_execution_time = fields.DatetimeField(null=True, description="最近执行时间", index=True)
    user_id = fields.IntField(description="创建用户ID", index=True)
    enable_dingtalk = fields.BooleanField(default=False, description="是否启用钉钉通知", index=True)

    class Meta:
        table = "api_test_plans"
        table_description = "接口测试计划表"


class ApiTestPlanCase(BaseModel, TimestampMixin):
    plan_id = fields.IntField(description="测试计划ID", index=True)
    case_id = fields.IntField(description="测试用例ID", index=True)
    execution_order = fields.IntField(default=0, description="执行顺序")
    execution_status = fields.CharField(max_length=20, default="normal", description="执行状态：normal-正常，skipped-跳过", index=True)
    execution_result = fields.CharField(max_length=20, null=True, description="执行结果：success-成功，failed-失败", index=True)
    execution_time = fields.DatetimeField(null=True, description="执行时间")
    response_time = fields.IntField(null=True, description="响应时间(毫秒)")
    error_message = fields.TextField(null=True, description="错误信息")
    # 新增响应相关字段
    status_code = fields.IntField(null=True, description="响应状态码")
    response_headers = fields.JSONField(null=True, description="响应头")
    response_body = fields.TextField(null=True, description="响应体")
    assertion_results = fields.JSONField(null=True, description="断言结果")

    class Meta:
        table = "api_test_plan_cases"
        table_description = "接口测试计划用例关联表"
        unique_together = (("plan_id", "case_id"),)


class FunctionalTestPlan(BaseModel, TimestampMixin):
    plan_name = fields.CharField(max_length=200, description="计划名称", index=True)
    level = fields.CharField(max_length=10, default="medium", description="等级：low-低，medium-中，high-高", index=True)
    status = fields.CharField(max_length=20, default="not_started", description="状态：not_started-未开始，in_progress-进行中，completed-已完成", index=True)
    description = fields.TextField(null=True, description="计划描述")
    project_id = fields.IntField(description="所属项目ID", index=True)
    user_id = fields.IntField(description="创建用户ID", index=True)
    pass_rate = fields.FloatField(null=True, description="通过率")

    class Meta:
        table = "functional_test_plans"
        table_description = "功能测试计划表"


class FunctionalTestPlanCase(BaseModel, TimestampMixin):
    plan_id = fields.IntField(description="测试计划ID", index=True)
    case_id = fields.IntField(description="测试用例ID", index=True)
    execution_order = fields.IntField(default=0, description="执行顺序")
    execution_status = fields.CharField(max_length=20, default="pending", description="执行状态：pending-待重试，passed-通过，failed-失败，blocked-阻塞，skipped-跳过", index=True)

    class Meta:
        table = "functional_test_plan_cases"
        table_description = "功能测试计划用例关联表"
        unique_together = (("plan_id", "case_id"),)


class ScheduledTask(BaseModel, TimestampMixin):
    task_name = fields.CharField(max_length=200, description="任务名称", index=True)
    plan_id = fields.IntField(description="测试计划ID", index=True)
    cron_expression = fields.CharField(max_length=100, description="Cron表达式")
    is_active = fields.BooleanField(default=True, description="是否启用", index=True)
    last_run_time = fields.DatetimeField(null=True, description="最后执行时间")
    next_run_time = fields.DatetimeField(null=True, description="下次执行时间")
    run_count = fields.IntField(default=0, description="执行次数")
    description = fields.TextField(null=True, description="任务描述")
    creator_id = fields.IntField(description="创建人ID", index=True)

    class Meta:
        table = "scheduled_task"
        table_description = "定时任务表"


class DingTalkConfig(BaseModel, TimestampMixin):
    """钉钉配置表"""
    plan_id = fields.IntField(description="测试计划ID", index=True)
    plan_type = fields.CharField(max_length=20, description="计划类型：api-接口测试计划，functional-功能测试计划", index=True)
    webhook_url = fields.CharField(max_length=500, description="钉钉机器人webhook地址")
    secret = fields.CharField(max_length=200, null=True, description="钉钉机器人密钥")
    send_condition = fields.CharField(max_length=20, default="all", description="发送条件：success-仅成功，failed-仅失败，all-全部", index=True)
    is_enabled = fields.BooleanField(default=True, description="是否启用", index=True)
    user_id = fields.IntField(description="创建用户ID", index=True)

    class Meta:
        table = "dingtalk_configs"
        table_description = "钉钉配置表"


class AIModelConfig(BaseModel, TimestampMixin):
    """AI大模型配置表"""
    name = fields.CharField(max_length=100, description="模型名称", index=True)
    model_type = fields.CharEnumField(AIModelType, description="模型类型", index=True)
    api_key = fields.CharField(max_length=500, description="API密钥")
    api_url = fields.CharField(max_length=500, null=True, description="API地址")
    model_name = fields.CharField(max_length=100, null=True, description="具体模型名称")
    max_tokens = fields.IntField(default=4096, description="最大token数")
    temperature = fields.FloatField(default=0.7, description="温度参数")
    timeout = fields.IntField(default=30, description="超时时间(秒)")
    status = fields.CharEnumField(AIModelStatus, default=AIModelStatus.INACTIVE, description="状态", index=True)
    is_default = fields.BooleanField(default=False, description="是否默认模型", index=True)
    description = fields.TextField(null=True, description="模型描述")
    config_json = fields.JSONField(null=True, description="额外配置参数(JSON格式)")
    user_id = fields.IntField(description="创建用户ID", index=True)

    class Meta:
        table = "ai_model_configs"
        table_description = "AI大模型配置表"


class PromptTemplate(BaseModel, TimestampMixin):
    """提示词模板表"""
    name = fields.CharField(max_length=100, description="模板名称", index=True)
    category = fields.CharField(max_length=50, description="模板分类", index=True)
    description = fields.TextField(null=True, description="模板描述")
    prompt_content = fields.TextField(description="提示词内容")
    variables = fields.JSONField(null=True, description="变量定义(JSON格式)")
    is_active = fields.BooleanField(default=True, description="是否启用", index=True)
    is_default = fields.BooleanField(default=False, description="是否默认模板", index=True)
    usage_count = fields.IntField(default=0, description="使用次数")
    user_id = fields.IntField(description="创建用户ID", index=True)

    class Meta:
        table = "prompt_templates"
        table_description = "提示词模板表"


class AITestCaseGeneration(BaseModel, TimestampMixin):
    """AI测试用例生成记录表"""
    task_name = fields.CharField(max_length=200, description="任务名称", index=True)
    requirement_description = fields.TextField(description="需求描述")
    prompt_template_id = fields.IntField(null=True, description="使用的提示词模板ID", index=True)
    ai_model_config_id = fields.IntField(description="使用的AI模型配置ID", index=True)
    project_id = fields.IntField(description="所属项目ID", index=True)
    module_id = fields.IntField(null=True, description="所属模块ID", index=True)
    generated_count = fields.IntField(default=0, description="生成的测试用例数量")
    generated_cases = fields.JSONField(null=True, description="生成的测试用例内容（JSON格式）")
    status = fields.CharField(max_length=20, default="pending", description="生成状态：pending-待生成，generating-生成中，completed-已完成，failed-失败", index=True)
    error_message = fields.TextField(null=True, description="错误信息")
    generation_time = fields.IntField(null=True, description="生成耗时(秒)")
    user_id = fields.IntField(description="创建用户ID", index=True)

    class Meta:
        table = "ai_test_case_generations"
        table_description = "AI测试用例生成记录表"


class DatabaseConnection(BaseModel, TimestampMixin):
    """数据库连接配置表"""
    name = fields.CharField(max_length=100, description="连接名称", index=True)
    db_type = fields.CharField(max_length=20, description="数据库类型：mysql,postgresql,sqlite,oracle", index=True)
    host = fields.CharField(max_length=255, description="主机地址")
    port = fields.IntField(description="端口号")
    database = fields.CharField(max_length=100, description="数据库名称")
    username = fields.CharField(max_length=100, description="用户名")
    password = fields.CharField(max_length=255, description="密码")
    charset = fields.CharField(max_length=20, default="utf8mb4", description="字符集")
    description = fields.TextField(null=True, description="连接描述")
    is_active = fields.BooleanField(default=True, description="是否启用", index=True)
    max_connections = fields.IntField(default=10, description="最大连接数")
    connection_timeout = fields.IntField(default=30, description="连接超时时间(秒)")
    user_id = fields.IntField(description="创建用户ID", index=True)

    class Meta:
        table = "database_connections"
        table_description = "数据库连接配置表"


class QueryHistory(BaseModel, TimestampMixin):
    """SQL查询历史记录表"""
    query_name = fields.CharField(max_length=200, null=True, description="查询名称", index=True)
    sql_content = fields.TextField(description="SQL语句内容")
    database_connection_id = fields.IntField(description="数据库连接ID", index=True)
    execution_time = fields.IntField(null=True, description="执行时间(毫秒)")
    affected_rows = fields.IntField(null=True, description="影响行数")
    result_count = fields.IntField(null=True, description="结果行数")
    status = fields.CharField(max_length=20, description="执行状态：success-成功，failed-失败", index=True)
    error_message = fields.TextField(null=True, description="错误信息")
    is_favorite = fields.BooleanField(default=False, description="是否收藏", index=True)
    user_id = fields.IntField(description="执行用户ID", index=True)

    class Meta:
        table = "query_history"
        table_description = "SQL查询历史记录表"



