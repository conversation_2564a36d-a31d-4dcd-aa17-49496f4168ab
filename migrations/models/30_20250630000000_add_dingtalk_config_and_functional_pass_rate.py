from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS `dingtalk_configs` (
            `id` BIGINT NOT NULL PRIMARY KEY AUTO_INCREMENT,
            `created_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
            `updated_at` DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
            `plan_id` INT NOT NULL COMMENT '测试计划ID',
            `plan_type` VARCHAR(20) NOT NULL COMMENT '计划类型：api-接口测试计划，functional-功能测试计划',
            `webhook_url` VARCHAR(500) NOT NULL COMMENT '钉钉机器人webhook地址',
            `secret` VARCHAR(200) NULL COMMENT '钉钉机器人密钥',
            `send_condition` VARCHAR(20) NOT NULL DEFAULT 'all' COMMENT '发送条件：success-仅成功，failed-仅失败，all-全部',
            `is_enabled` BOOL NOT NULL DEFAULT 1 COMMENT '是否启用',
            `user_id` INT NOT NULL COMMENT '创建用户ID',
            KEY `idx_dingtalk_co_created_4ba46b` (`created_at`),
            KEY `idx_dingtalk_co_updated_c9d346` (`updated_at`),
            KEY `idx_dingtalk_co_plan_id_3f0aae` (`plan_id`),
            KEY `idx_dingtalk_co_plan_ty_ca70fd` (`plan_type`),
            KEY `idx_dingtalk_co_send_co_64adfe` (`send_condition`),
            KEY `idx_dingtalk_co_is_enab_f5ff3d` (`is_enabled`),
            KEY `idx_dingtalk_co_user_id_47150c` (`user_id`)
        ) CHARACTER SET utf8mb4 COMMENT='钉钉配置表';
        
        ALTER TABLE `functional_test_plans` 
        ADD COLUMN `pass_rate` DOUBLE NULL COMMENT '通过率';
    """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS `dingtalk_configs`;
        ALTER TABLE `functional_test_plans` DROP COLUMN `pass_rate`;
    """
