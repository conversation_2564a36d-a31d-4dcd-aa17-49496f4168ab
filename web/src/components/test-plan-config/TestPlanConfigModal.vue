<template>
  <NModal :show="visible" @update:show="handleVisibleChange" preset="dialog" title="测试计划配置" style="width: 800px;">
    <NTabs v-model:value="activeTab" type="line" animated>
      <!-- 钉钉配置标签页 -->
      <NTabPane name="dingtalk" tab="钉钉配置">
        <NForm
          ref="dingtalkFormRef"
          :model="dingtalkFormData"
          :rules="dingtalkRules"
          label-placement="left"
          label-width="120px"
          require-mark-placement="right-hanging"
        >
          <NFormItem label="Webhook地址" path="webhook_url">
            <NInput
              v-model:value="dingtalkFormData.webhook_url"
              placeholder="请输入钉钉机器人Webhook地址"
              clearable
            />
          </NFormItem>
          
          <NFormItem label="密钥" path="secret">
            <NInput
              v-model:value="dingtalkFormData.secret"
              placeholder="请输入钉钉机器人密钥（可选）"
              clearable
              show-password-on="click"
            />
          </NFormItem>
          
          <NFormItem label="发送条件" path="send_condition">
            <NSelect
              v-model:value="dingtalkFormData.send_condition"
              :options="sendConditionOptions"
              placeholder="请选择发送条件"
            />
          </NFormItem>
          
          <NFormItem label="启用状态" path="is_enabled">
            <NSwitch v-model:value="dingtalkFormData.is_enabled">
              <template #checked>启用</template>
              <template #unchecked>禁用</template>
            </NSwitch>
          </NFormItem>
        </NForm>
        
        <template #suffix>
          <NSpace>
            <NButton type="info" @click="handleTestDingtalk" :loading="dingtalkTestLoading">测试配置</NButton>
            <NButton type="primary" @click="handleSaveDingtalk" :loading="dingtalkSaveLoading">保存钉钉配置</NButton>
          </NSpace>
        </template>
      </NTabPane>

      <!-- 定时任务配置标签页 -->
      <NTabPane name="scheduled" tab="定时任务配置">
        <NForm
          ref="scheduledFormRef"
          :model="scheduledFormData"
          :rules="scheduledRules"
          label-placement="left"
          label-width="120px"
          require-mark-placement="right-hanging"
        >
          <NFormItem label="任务名称" path="task_name">
            <NInput
              v-model:value="scheduledFormData.task_name"
              placeholder="请输入任务名称"
              clearable
            />
          </NFormItem>
          
          <NFormItem label="执行频率" path="frequency_type">
            <NSelect
              v-model:value="cronConfig.type"
              :options="frequencyOptions"
              placeholder="请选择执行频率"
              @update:value="handleFrequencyChange"
            />
          </NFormItem>
          
          <!-- 每日执行配置 -->
          <NFormItem v-if="cronConfig.type === 'daily'" label="执行时间" path="time">
            <NTimePicker
              v-model:formatted-value="cronConfig.time"
              value-format="HH:mm"
              format="HH:mm"
              placeholder="选择执行时间"
            />
          </NFormItem>
          
          <!-- 每周执行配置 -->
          <template v-if="cronConfig.type === 'weekly'">
            <NFormItem label="执行时间" path="time">
              <NTimePicker
                v-model:formatted-value="cronConfig.time"
                value-format="HH:mm"
                format="HH:mm"
                placeholder="选择执行时间"
              />
            </NFormItem>
            <NFormItem label="执行日期" path="weekdays">
              <NCheckboxGroup v-model:value="cronConfig.weekdays">
                <NSpace>
                  <NCheckbox :value="1">周一</NCheckbox>
                  <NCheckbox :value="2">周二</NCheckbox>
                  <NCheckbox :value="3">周三</NCheckbox>
                  <NCheckbox :value="4">周四</NCheckbox>
                  <NCheckbox :value="5">周五</NCheckbox>
                  <NCheckbox :value="6">周六</NCheckbox>
                  <NCheckbox :value="7">周日</NCheckbox>
                </NSpace>
              </NCheckboxGroup>
            </NFormItem>
          </template>
          
          <!-- 每月执行配置 -->
          <template v-if="cronConfig.type === 'monthly'">
            <NFormItem label="执行时间" path="time">
              <NTimePicker
                v-model:formatted-value="cronConfig.time"
                value-format="HH:mm"
                format="HH:mm"
                placeholder="选择执行时间"
              />
            </NFormItem>
            <NFormItem label="执行日期" path="dayOfMonth">
              <NInputNumber
                v-model:value="cronConfig.dayOfMonth"
                :min="1"
                :max="31"
                placeholder="请输入日期"
              />
            </NFormItem>
          </template>
          
          <!-- 自定义Cron表达式 -->
          <NFormItem v-if="cronConfig.type === 'custom'" label="Cron表达式" path="cron_expression">
            <NInput
              v-model:value="scheduledFormData.cron_expression"
              placeholder="请输入Cron表达式，如：0 9 * * 1-5"
              clearable
            />
            <template #feedback>
              <div style="font-size: 12px; color: #999; margin-top: 4px;">
                格式：分 时 日 月 周，例如：0 9 * * 1-5 表示周一到周五的9点执行
              </div>
            </template>
          </NFormItem>
          
          <NFormItem label="任务描述" path="description">
            <NInput
              v-model:value="scheduledFormData.description"
              type="textarea"
              placeholder="请输入任务描述"
              :rows="3"
            />
          </NFormItem>
        </NForm>
        
        <template #suffix>
          <NSpace>
            <NButton v-if="existingScheduledTask" type="error" @click="handleDeleteScheduledTask" :loading="scheduledDeleteLoading">删除定时任务</NButton>
            <NButton type="primary" @click="handleSaveScheduledTask" :loading="scheduledSaveLoading">
              {{ existingScheduledTask ? '更新定时任务' : '创建定时任务' }}
            </NButton>
          </NSpace>
        </template>
      </NTabPane>
    </NTabs>
    
    <template #action>
      <NSpace>
        <NButton @click="handleCancel">关闭</NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { 
  NModal, NTabs, NTabPane, NForm, NFormItem, NInput, NSelect, NSwitch, 
  NButton, NSpace, NTimePicker, NCheckboxGroup, NCheckbox, NInputNumber,
  useMessage 
} from 'naive-ui'
import dingtalkConfigApi from '@/api/dingtalkConfig'
import scheduledTaskApi from '@/api/scheduledTask'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  planId: {
    type: Number,
    required: true
  },
  planType: {
    type: String,
    required: true,
    validator: (value) => ['api', 'functional'].includes(value)
  },
  planName: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:visible', 'saved'])

const $message = useMessage()
const activeTab = ref('dingtalk')

// 钉钉配置相关
const dingtalkFormRef = ref(null)
const dingtalkSaveLoading = ref(false)
const dingtalkTestLoading = ref(false)

const dingtalkFormData = ref({
  plan_id: props.planId,
  plan_type: props.planType,
  webhook_url: '',
  secret: '',
  send_condition: 'all',
  is_enabled: true
})

const sendConditionOptions = ref([
  { label: '全部发送', value: 'all' },
  { label: '仅成功时发送', value: 'success_only' },
  { label: '仅失败时发送', value: 'failure_only' }
])

const dingtalkRules = {
  webhook_url: [
    { required: true, message: '请输入Webhook地址', trigger: 'blur' }
  ]
}

// 定时任务配置相关
const scheduledFormRef = ref(null)
const scheduledSaveLoading = ref(false)
const scheduledDeleteLoading = ref(false)
const existingScheduledTask = ref(null)

const scheduledFormData = ref({
  task_name: '',
  cron_expression: '',
  description: ''
})

const cronConfig = ref({
  type: 'daily',
  time: '09:00',
  weekdays: [1, 2, 3, 4, 5],
  dayOfMonth: 1,
  interval: 1
})

const frequencyOptions = ref([
  { label: '每日执行', value: 'daily' },
  { label: '每周执行', value: 'weekly' },
  { label: '每月执行', value: 'monthly' },
  { label: '自定义', value: 'custom' }
])

const scheduledRules = {
  task_name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ],
  cron_expression: [
    { 
      required: true, 
      message: '请输入Cron表达式', 
      trigger: 'blur',
      validator: (rule, value) => {
        if (cronConfig.value.type === 'custom' && !value) {
          return new Error('请输入Cron表达式')
        }
        return true
      }
    }
  ]
}

// 处理弹窗显示状态变化
const handleVisibleChange = (value) => {
  emit('update:visible', value)
}

// 处理取消
const handleCancel = () => {
  emit('update:visible', false)
}

// 监听弹窗显示状态
watch(() => props.visible, async (newVal) => {
  if (newVal) {
    activeTab.value = 'dingtalk'
    await loadConfigs()
  }
})

// 加载配置
const loadConfigs = async () => {
  await Promise.all([
    loadDingtalkConfig(),
    loadScheduledTaskConfig()
  ])
}

// 加载钉钉配置
const loadDingtalkConfig = async () => {
  try {
    const { data } = await dingtalkConfigApi.getDingTalkConfig({
      plan_id: props.planId,
      plan_type: props.planType
    })
    
    if (data) {
      dingtalkFormData.value = {
        ...dingtalkFormData.value,
        webhook_url: data.webhook_url || '',
        secret: data.secret || '',
        send_condition: data.send_condition || 'all',
        is_enabled: data.is_enabled !== undefined ? data.is_enabled : true
      }
    }
  } catch (error) {
    console.error('加载钉钉配置失败:', error)
  }
}

// 加载定时任务配置
const loadScheduledTaskConfig = async () => {
  try {
    const { data } = await scheduledTaskApi.getTasksByPlan({ plan_id: props.planId })
    
    if (data && data.length > 0) {
      existingScheduledTask.value = data[0]
      scheduledFormData.value = {
        id: data[0].id,
        task_name: data[0].task_name,
        cron_expression: data[0].cron_expression,
        description: data[0].description || ''
      }
      parseCronExpression(data[0].cron_expression)
    } else {
      existingScheduledTask.value = null
      scheduledFormData.value = {
        task_name: `${props.planName || '测试计划'}_定时任务`,
        cron_expression: generateCronExpression(),
        description: `定时执行测试计划：${props.planName || ''}`
      }
    }
  } catch (error) {
    console.error('加载定时任务配置失败:', error)
  }
}

// 生成Cron表达式
const generateCronExpression = () => {
  const [hour, minute] = cronConfig.value.time.split(':')
  
  switch (cronConfig.value.type) {
    case 'daily':
      return `${minute} ${hour} * * *`
    case 'weekly':
      const weekdays = cronConfig.value.weekdays.join(',')
      return `${minute} ${hour} * * ${weekdays}`
    case 'monthly':
      return `${minute} ${hour} ${cronConfig.value.dayOfMonth} * *`
    default:
      return scheduledFormData.value.cron_expression || '0 9 * * *'
  }
}

// 解析Cron表达式
const parseCronExpression = (cronExpression) => {
  const parts = cronExpression.split(' ')
  if (parts.length >= 5) {
    const [minute, hour, day, month, weekday] = parts
    cronConfig.value.time = `${hour.padStart(2, '0')}:${minute.padStart(2, '0')}`
    
    if (day === '*' && month === '*' && weekday === '*') {
      cronConfig.value.type = 'daily'
    } else if (day === '*' && month === '*' && weekday !== '*') {
      cronConfig.value.type = 'weekly'
      cronConfig.value.weekdays = weekday.split(',').map(Number)
    } else if (day !== '*' && month === '*' && weekday === '*') {
      cronConfig.value.type = 'monthly'
      cronConfig.value.dayOfMonth = parseInt(day)
    } else {
      cronConfig.value.type = 'custom'
    }
  }
}

// 处理频率变化
const handleFrequencyChange = () => {
  if (cronConfig.value.type !== 'custom') {
    scheduledFormData.value.cron_expression = generateCronExpression()
  }
}

// 监听配置变化，自动生成Cron表达式
watch(() => [cronConfig.value.time, cronConfig.value.weekdays, cronConfig.value.dayOfMonth], () => {
  if (cronConfig.value.type !== 'custom') {
    scheduledFormData.value.cron_expression = generateCronExpression()
  }
}, { deep: true })

// 钉钉配置相关方法
const handleTestDingtalk = async () => {
  try {
    await dingtalkFormRef.value?.validate()
    dingtalkTestLoading.value = true
    
    await dingtalkConfigApi.testDingTalkConfig(dingtalkFormData.value)
    $message.success('钉钉配置测试成功')
  } catch (error) {
    if (error.message) {
      $message.error(error.message)
    } else {
      $message.error('钉钉配置测试失败')
    }
  } finally {
    dingtalkTestLoading.value = false
  }
}

const handleSaveDingtalk = async () => {
  try {
    await dingtalkFormRef.value?.validate()
    dingtalkSaveLoading.value = true
    
    await dingtalkConfigApi.saveDingTalkConfig(dingtalkFormData.value)
    $message.success('钉钉配置保存成功')
    
    emit('saved')
  } catch (error) {
    if (error.message) {
      $message.error(error.message)
    } else {
      $message.error('钉钉配置保存失败')
    }
  } finally {
    dingtalkSaveLoading.value = false
  }
}

// 定时任务相关方法
const handleSaveScheduledTask = async () => {
  try {
    await scheduledFormRef.value?.validate()
    scheduledSaveLoading.value = true
    
    // 确保使用最新的Cron表达式
    if (cronConfig.value.type !== 'custom') {
      scheduledFormData.value.cron_expression = generateCronExpression()
    }
    
    let result
    if (scheduledFormData.value.id) {
      // 更新现有定时任务
      result = await scheduledTaskApi.updateScheduledTask({
        id: scheduledFormData.value.id,
        task_name: scheduledFormData.value.task_name,
        cron_expression: scheduledFormData.value.cron_expression,
        description: scheduledFormData.value.description
      })
    } else {
      // 创建新定时任务
      result = await scheduledTaskApi.createScheduledTask({
        task_name: scheduledFormData.value.task_name,
        plan_id: props.planId,
        cron_expression: scheduledFormData.value.cron_expression,
        description: scheduledFormData.value.description
      })
    }
    
    if (result.code === 200) {
      $message.success(existingScheduledTask.value ? '定时任务更新成功' : '定时任务创建成功')
      await loadScheduledTaskConfig() // 重新加载配置
      emit('saved')
    } else {
      $message.error(result.msg || '操作失败')
    }
  } catch (error) {
    console.error('保存定时任务失败:', error)
    $message.error('保存定时任务失败: ' + error.message)
  } finally {
    scheduledSaveLoading.value = false
  }
}

const handleDeleteScheduledTask = async () => {
  if (!existingScheduledTask.value) return
  
  try {
    scheduledDeleteLoading.value = true
    const result = await scheduledTaskApi.deleteScheduledTask({
      task_id: existingScheduledTask.value.id
    })
    
    if (result.code === 200) {
      $message.success('定时任务删除成功')
      existingScheduledTask.value = null
      // 重置表单
      scheduledFormData.value = {
        task_name: `${props.planName || '测试计划'}_定时任务`,
        cron_expression: generateCronExpression(),
        description: `定时执行测试计划：${props.planName || ''}`
      }
      emit('saved')
    } else {
      $message.error(result.msg || '删除定时任务失败')
    }
  } catch (error) {
    console.error('删除定时任务失败:', error)
    $message.error('删除定时任务失败: ' + error.message)
  } finally {
    scheduledDeleteLoading.value = false
  }
}
</script>

<style scoped>
:deep(.n-tab-pane) {
  padding: 16px 0;
}

:deep(.n-form-item-feedback-wrapper) {
  min-height: auto;
}
</style>
