<template>
  <NModal v-model:show="modalVisible" preset="dialog" title="钉钉配置" style="width: 600px;">
    <NForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-placement="left"
      label-width="120px"
      require-mark-placement="right-hanging"
    >
      <NFormItem label="Webhook地址" path="webhook_url">
        <NInput
          v-model:value="formData.webhook_url"
          placeholder="请输入钉钉机器人Webhook地址"
          clearable
        />
      </NFormItem>
      
      <NFormItem label="密钥" path="secret">
        <NInput
          v-model:value="formData.secret"
          placeholder="请输入钉钉机器人密钥（可选）"
          clearable
          show-password-on="click"
        />
      </NFormItem>
      
      <NFormItem label="发送条件" path="send_condition">
        <NSelect
          v-model:value="formData.send_condition"
          :options="sendConditionOptions"
          placeholder="请选择发送条件"
        />
      </NFormItem>
      
      <NFormItem label="启用状态" path="is_enabled">
        <NSwitch v-model:value="formData.is_enabled">
          <template #checked>启用</template>
          <template #unchecked>禁用</template>
        </NSwitch>
      </NFormItem>
    </NForm>
    
    <template #action>
      <NSpace>
        <NButton @click="handleCancel">取消</NButton>
        <NButton type="info" @click="handleTest" :loading="testLoading">测试配置</NButton>
        <NButton type="primary" @click="handleSave" :loading="saveLoading">保存</NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { NModal, NForm, NFormItem, NInput, NSelect, NSwitch, NButton, NSpace, useMessage } from 'naive-ui'
import dingtalkConfigApi from '@/api/dingtalkConfig'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  planId: {
    type: Number,
    required: true
  },
  planType: {
    type: String,
    required: true,
    validator: (value) => ['api', 'functional'].includes(value)
  }
})

const emit = defineEmits(['update:visible', 'saved'])

// 计算属性处理modal显示状态
const modalVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const $message = useMessage()
const formRef = ref(null)
const saveLoading = ref(false)
const testLoading = ref(false)

// 表单数据
const formData = ref({
  plan_id: props.planId,
  plan_type: props.planType,
  webhook_url: '',
  secret: '',
  send_condition: 'all',
  is_enabled: true
})

// 发送条件选项
const sendConditionOptions = [
  { label: '全部发送', value: 'all' },
  { label: '仅成功时发送', value: 'success' },
  { label: '仅失败时发送', value: 'failed' }
]

// 表单验证规则
const rules = {
  webhook_url: [
    { required: true, message: '请输入Webhook地址', trigger: 'blur' },
    { 
      pattern: /^https:\/\/oapi\.dingtalk\.com\/robot\/send\?access_token=.+/, 
      message: '请输入有效的钉钉机器人Webhook地址', 
      trigger: 'blur' 
    }
  ],
  send_condition: [
    { required: true, message: '请选择发送条件', trigger: 'change' }
  ]
}

// 监听弹窗显示状态
watch(() => props.visible, async (newVal) => {
  if (newVal) {
    // 重置表单数据
    formData.value = {
      plan_id: props.planId,
      plan_type: props.planType,
      webhook_url: '',
      secret: '',
      send_condition: 'all',
      is_enabled: true
    }
    
    // 加载现有配置
    await loadConfig()
  }
})

// 加载现有配置
const loadConfig = async () => {
  try {
    const { data } = await dingtalkConfigApi.getDingTalkConfig({
      plan_id: props.planId,
      plan_type: props.planType
    })
    
    if (data) {
      formData.value = {
        ...formData.value,
        webhook_url: data.webhook_url || '',
        secret: data.secret || '',
        send_condition: data.send_condition || 'all',
        is_enabled: data.is_enabled !== undefined ? data.is_enabled : true
      }
    }
  } catch (error) {
    console.error('加载钉钉配置失败:', error)
  }
}

// 测试配置
const handleTest = async () => {
  try {
    await formRef.value?.validate()
    testLoading.value = true
    
    await dingtalkConfigApi.testDingTalkConfig(formData.value)
    $message.success('钉钉配置测试成功')
  } catch (error) {
    if (error.message) {
      $message.error(error.message)
    } else {
      $message.error('钉钉配置测试失败')
    }
  } finally {
    testLoading.value = false
  }
}

// 保存配置
const handleSave = async () => {
  try {
    await formRef.value?.validate()
    saveLoading.value = true
    
    await dingtalkConfigApi.saveDingTalkConfig(formData.value)
    $message.success('钉钉配置保存成功')
    
    emit('saved')
    handleCancel()
  } catch (error) {
    if (error.message) {
      $message.error(error.message)
    } else {
      $message.error('钉钉配置保存失败')
    }
  } finally {
    saveLoading.value = false
  }
}

// 取消
const handleCancel = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
:deep(.n-form-item-label) {
  font-weight: 500;
}
</style>
