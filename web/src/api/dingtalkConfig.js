import { request } from '@/utils'

export default {
  /**
   * 获取钉钉配置
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回钉钉配置数据
   */
  getDingTalkConfig(params) {
    return request.get('/dingtalk_config/get', { params })
  },

  /**
   * 保存钉钉配置
   * @param {Object} data - 钉钉配置数据
   * @returns {Promise<Object>} - 返回保存结果
   */
  saveDingTalkConfig(data) {
    return request.post('/dingtalk_config/save', data)
  },

  /**
   * 删除钉钉配置
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} - 返回删除结果
   */
  deleteDingTalkConfig(params) {
    return request.delete('/dingtalk_config/delete', { params })
  },

  /**
   * 测试钉钉配置
   * @param {Object} data - 钉钉配置数据
   * @returns {Promise<Object>} - 返回测试结果
   */
  testDingTalkConfig(data) {
    return request.post('/dingtalk_config/test', data)
  }
}
